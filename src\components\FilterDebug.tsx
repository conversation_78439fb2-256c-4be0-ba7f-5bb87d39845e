import React from 'react';

interface FilterDebugProps {
  rawDataLength: number;
  filteredDataLength: number;
  filters: {
    zone?: string;
    location?: string;
    dealer?: string;
    areaOffice?: string;
    category?: string;
  };
  msiData: any;
}

const FilterDebug: React.FC<FilterDebugProps> = ({ 
  rawDataLength, 
  filteredDataLength, 
  filters, 
  msiData 
}) => {
  return (
    <div className="bg-yellow-100 border border-yellow-400 rounded p-4 mb-4">
      <h3 className="font-bold text-yellow-800 mb-2">Filter Debug Info</h3>
      <div className="text-sm text-yellow-700">
        <p><strong>Raw Data Length:</strong> {rawDataLength}</p>
        <p><strong>Filtered Data Length:</strong> {filteredDataLength}</p>
        <p><strong>MSI Data Categories:</strong> {msiData?.categories?.length || 0}</p>
        <div className="mt-2">
          <strong>Active Filters:</strong>
          <ul className="ml-4">
            <li>Zone: {filters.zone || 'All'}</li>
            <li>Location: {filters.location || 'All'}</li>
            <li>Dealer: {filters.dealer || 'All'}</li>
            <li>Area Office: {filters.areaOffice || 'All'}</li>
            <li>Category: {filters.category || 'All'}</li>
          </ul>
        </div>
        {msiData?.totals && (
          <div className="mt-2">
            <strong>Totals:</strong>
            <ul className="ml-4">
              <li>Sales: {msiData.totals.sales?.percentage || 0}%</li>
              <li>Service: {msiData.totals.service?.percentage || 0}%</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default FilterDebug;
