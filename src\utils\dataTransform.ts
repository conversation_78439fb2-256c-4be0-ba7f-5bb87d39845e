// API endpoint for fetching dealer checklist submissions
const API_ENDPOINT = 'https://api.eisqr.com/dealer-checklist-submissions/latest-by-vendor';

// Function to fetch data from API
async function fetchPreApiData() {
  try {
    const response = await fetch(API_ENDPOINT);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();

    // Parse the stringified response field for each object
    return data.map((item: any) => ({
      ...item,
      response: typeof item.response === 'string' ? JSON.parse(item.response) : item.response
    }));
  } catch (error) {
    console.error('Error fetching preAPI data:', error);
    throw error;
  }
}

// Mapping data for categories and zones
const categoryList = [
  { name: 'Forging & Machining', value: 1 },
  { name: 'Casting & Machining', value: 2 },
  { name: 'Pressing & Fabrication', value: 3 },
  { name: 'Proprietary Mechanical', value: 4 },
  { name: 'Proprietary Electrical', value: 5 },
  { name: 'Plastics, Rubber, Painting and Stickers', value: 6 },
  { name: 'EV/3W/2W', value: 7 },
  { name: 'BW', value: 8 },
  { name: 'Accessories', value: 9 }
];

const zonalOfficeList = [
  { name: "Central", value: 1 },
  { name: "East", value: 2 },
  { name: "North", value: 3 },
  { name: "South", value: 9 },
  { name: "South1", value: 4 },
  { name: "South2", value: 5 },
  { name: "West", value: 8 },
  { name: "West1", value: 6 },
  { name: "West2", value: 7 }
];

// Helper functions to map values to names
const getCategoryName = (value: number): string => {
  const category = categoryList.find(cat => cat.value === value);
  return category ? category.name : `Category ${value}`;
};

const getZoneName = (value: number): string => {
  const zone = zonalOfficeList.find(zone => zone.value === value);
  return zone ? zone.name : `Zone ${value}`;
};

// Function to extract filter options from API data
export async function getFilterOptions() {
  try {
    const dataArray = await fetchPreApiData();

    const zones = new Set<string>();
    const locations = new Set<string>();
    const dealers = new Set<string>();
    const areaOffices = new Set<string>();
    const categories = new Set<string>();

    console.log('Extracting filter options from API data:', dataArray.length, 'objects');
    console.log('First data object structure:', dataArray[0]);

    // Extract unique values from all objects in the array
    dataArray.forEach(dataObject => {
      // Get vendor details using the helper function that works with actual API structure
      const vendorDetails = getVendorDetails(dataArray, dataObject.vendorCode, dataObject.dealerId);

      // Extract zone name
      if (vendorDetails.zone) {
        zones.add(vendorDetails.zone);
      }

      // Extract location (city)
      if (vendorDetails.city) {
        locations.add(vendorDetails.city);
      }

      // Extract dealer name
      if (vendorDetails.dealerName) {
        dealers.add(vendorDetails.dealerName);
      }

      // Extract area office
      if (vendorDetails.areaOffice) {
        areaOffices.add(vendorDetails.areaOffice);
      }

      // Extract category
      if (vendorDetails.category) {
        categories.add(vendorDetails.category);
      }
    });

    const filterOptions = {
      zones: ['All', ...Array.from(zones).sort()],
      locations: ['All', ...Array.from(locations).sort()],
      dealers: ['All', ...Array.from(dealers).sort()],
      areaOffices: ['All', ...Array.from(areaOffices).sort()],
      categories: ['All', ...Array.from(categories).sort()]
    };

    console.log('Filter options extracted:', filterOptions);

    return {
      zones: ['All', ...Array.from(zones).sort()],
      locations: ['All', ...Array.from(locations).sort()],
      dealers: ['All', ...Array.from(dealers).sort()],
      areaOffices: ['All', ...Array.from(areaOffices).sort()],
      categories: ['All', ...Array.from(categories).sort()]
    };
  } catch (error) {
    console.error('Error extracting filter options:', error);
    return {
      zones: ['All'],
      locations: ['All'],
      dealers: ['All'],
      areaOffices: ['All'],
      categories: ['All']
    };
  }
}

// Function to get filtered options based on current selections
export async function getFilteredOptions(currentFilters: {
  zone?: string;
  location?: string;
  dealer?: string;
  category?: string;
}) {
  try {
    const dataArray = await fetchPreApiData();

    const locations = new Set<string>();
    const dealers = new Set<string>();
    const areaOffices = new Set<string>();
    const categories = new Set<string>();

    console.log('Filtering options with current filters:', currentFilters);

    // Filter data based on current selections
    dataArray.forEach(dataObject => {
      // Get vendor details using the helper function
      const vendorDetails = getVendorDetails(dataArray, dataObject.vendorCode, dataObject.dealerId);

      // Apply zone filter
      if (currentFilters.zone && currentFilters.zone !== 'All') {
        if (vendorDetails.zone !== currentFilters.zone) {
          return;
        }
      }

      // Apply location filter
      if (currentFilters.location && currentFilters.location !== 'All' &&
        vendorDetails.city !== currentFilters.location) {
        return;
      }

      // Apply dealer filter
      if (currentFilters.dealer && currentFilters.dealer !== 'All' &&
        vendorDetails.dealerName !== currentFilters.dealer) {
        return;
      }

      // Apply category filter
      if (currentFilters.category && currentFilters.category !== 'All' &&
        vendorDetails.category !== currentFilters.category) {
        return;
      }

      // Add to available options if passes all filters
      if (vendorDetails.city) locations.add(vendorDetails.city);
      if (vendorDetails.dealerName) dealers.add(vendorDetails.dealerName);
      if (vendorDetails.areaOffice) areaOffices.add(vendorDetails.areaOffice);
      if (vendorDetails.category) categories.add(vendorDetails.category);
    });

    const filteredOptions = {
      locations: ['All', ...Array.from(locations).sort()],
      dealers: ['All', ...Array.from(dealers).sort()],
      areaOffices: ['All', ...Array.from(areaOffices).sort()],
      categories: ['All', ...Array.from(categories).sort()]
    };

    console.log('Filtered options result:', filteredOptions);

    return filteredOptions;
  } catch (error) {
    console.error('Error getting filtered options:', error);
    return {
      locations: ['All'],
      dealers: ['All'],
      areaOffices: ['All'],
      categories: ['All']
    };
  }
}

interface PreApiQuestion {
  label: string;
  name: string;
  options: Array<{
    label: string;
    checked: number;
    score: number;
  }>;
  type: string;
}

interface PreApiGroup {
  type: string;
  label: string;
  name: string;
  criteria: string;
  subCriteria: string;
  maxScore: number;
  section: number;
  questions: PreApiQuestion[];
  score?: number;
  enabled?: boolean;
}

// Helper function to extract reference number from question label
function extractReferenceNumber(label: string): string {
  const cleanedLabel = label.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, '&');
  const match = cleanedLabel.match(/^([A-Z]\d+[A-Z]*)\.\s*/);
  return match ? match[1] : '';
}

// Helper function to clean HTML tags and alphanumeric IDs from labels
function cleanLabel(label: string): string {
  return label
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
    .replace(/&amp;/g, '&') // Replace &amp; with &
    .replace(/^[A-Z]\d+\.\s*/, '') // Remove alphanumeric patterns like A1., B2., etc. at the start
    .replace(/^[A-Z]\d+[A-Z]*\.\s*/, '') // Remove patterns like A1A., B2C., etc. at the start
    .trim();
}

// Helper function to calculate actual score for a question
function calculateQuestionScore(question: PreApiQuestion): number {
  if (!question || !question.options) return 0;
  const selectedOption = question.options.find(option => option.checked === 1);
  return selectedOption && typeof selectedOption.score === 'number' ? selectedOption.score : 0;
}

// Helper function to get max score for a question
function getQuestionMaxScore(question: PreApiQuestion): number {
  if (!question || !question.options || question.options.length === 0) return 1;
  const scores = question.options.map(option => typeof option.score === 'number' ? option.score : 0);
  return Math.max(...scores) || 1; // Ensure at least 1 to avoid division by zero
}

// Helper function to get selected response for a question
function getQuestionResponse(question: PreApiQuestion): string {
  const selectedOption = question.options.find(option => option.checked === 1);
  return selectedOption ? cleanLabel(selectedOption.label) : 'No Response';
}

// Function to consolidate data from multiple API objects and calculate averages
function consolidateApiData(dataArray: any[]) {
  if (dataArray.length === 0) {
    throw new Error('No data to consolidate');
  }

  // Use the first object as the base structure
  const baseObject = dataArray[0];

  // If there's only one object, return it as is
  if (dataArray.length === 1) {
    return baseObject;
  }

  // Consolidate scores by calculating averages
  let totalSalesScore = 0;
  let totalServiceScore = 0;
  let totalOverallScore = 0;
  let validScoreCount = 0;

  // Collect all responses for merging
  const allResponses: any[] = [];

  dataArray.forEach(dataObject => {
    if (dataObject.response) {
      // Parse the stringified JSON response
      let parsedResponse;
      try {
        parsedResponse = typeof dataObject.response === 'string'
          ? JSON.parse(dataObject.response)
          : dataObject.response;

        if (Array.isArray(parsedResponse)) {
          allResponses.push(...parsedResponse);
        }
      } catch (error) {
        console.error('Error parsing response JSON:', error);
      }
    }

    // Handle different score structures
    if (dataObject.score) {
      totalSalesScore += dataObject.score.salesScore || 0;
      totalServiceScore += dataObject.score.serviceScore || 0;
      totalOverallScore += dataObject.score.overallScore || 0;
      validScoreCount++;
    } else {
      // If no score object, try to calculate from response data
      let parsedResponse;
      try {
        parsedResponse = typeof dataObject.response === 'string'
          ? JSON.parse(dataObject.response)
          : dataObject.response;
      } catch (error) {
        console.error('Error parsing response for score calculation:', error);
        parsedResponse = [];
      }

      if (parsedResponse && Array.isArray(parsedResponse)) {
        let salesScore = 0;
        let serviceScore = 0;

        parsedResponse.forEach((item: any) => {
          if (item.type === 'checklist-group' && item.score) {
            if (item.section === 1) {
              salesScore += item.score;
            } else if (item.section === 2) {
              serviceScore += item.score;
            }
          }
        });

        totalSalesScore += salesScore;
        totalServiceScore += serviceScore;
        totalOverallScore += (salesScore + serviceScore) / 2;
        validScoreCount++;
      }
    }
  });

  // Calculate average scores
  const avgSalesScore = validScoreCount > 0 ? totalSalesScore / validScoreCount : 0;
  const avgServiceScore = validScoreCount > 0 ? totalServiceScore / validScoreCount : 0;
  const avgOverallScore = validScoreCount > 0 ? totalOverallScore / validScoreCount : 0;

  // Return consolidated object with averaged scores
  return {
    ...baseObject,
    response: allResponses,
    score: {
      salesScore: Math.round(avgSalesScore * 100) / 100, // Round to 2 decimal places
      serviceScore: Math.round(avgServiceScore * 100) / 100,
      overallScore: Math.round(avgOverallScore * 100) / 100
    }
  };
}

// Get vendor details from API data
const getVendorDetails = (dataArray: any[], vendorCode: string, dealerId: number) => {
  // Find the data object that matches the vendorCode and dealerId
  const matchingData = dataArray.find(dataObject =>
    dataObject.vendorCode === vendorCode && dataObject.dealerId === dealerId
  );

  console.log('Getting vendor details for:', { vendorCode, dealerId });
  console.log('Matching data found:', !!matchingData);
  console.log('Vendor object exists:', !!matchingData?.vendor);

  if (matchingData && matchingData.vendor) {
    const vendorDetails = {
      zone: getZoneName(matchingData.vendor.dealerZone),
      city: matchingData.vendor.dealerLocation,
      dealerName: matchingData.vendor.dealerName,
      dealerCode: `${matchingData.vendor.dealerName.substring(0, 3).toUpperCase()}-${String(dealerId).padStart(3, '0')}`,
      areaOffice: matchingData.vendor.dealerAO,
      category: getCategoryName(matchingData.vendor.dealerCategory)
    };

    console.log('Vendor details extracted:', vendorDetails);
    return vendorDetails;
  }

  // Fallback if no matching data found
  return {
    zone: 'Unknown Zone',
    city: 'Unknown Location',
    dealerName: `Dealer ${dealerId}`,
    dealerCode: `DLR-${String(dealerId).padStart(3, '0')}`,
    areaOffice: 'Unknown Area Office',
    category: 'Unknown Category'
  };
};

// Transform API data to match the existing msiData.json structure
export async function transformPreApiData() {
  try {
    const dataArray = await fetchPreApiData();

    if (!dataArray || dataArray.length === 0) {
      throw new Error('No data received from API');
    }

    // Consolidate all data from submissions
    const consolidatedData = consolidateApiData(dataArray);
    const response = consolidatedData.response;

    // Separate groups
    const salesGroups: PreApiGroup[] = [];
    const serviceGroups: PreApiGroup[] = [];

    response.forEach((item: any) => {
      if (item.type === 'checklist-group') {
        if (item.section === 1) salesGroups.push(item);
        if (item.section === 2) serviceGroups.push(item);
      }
    });

    // Group by criteria
    const criteriaMap = new Map<string, { sales: PreApiGroup[], service: PreApiGroup[] }>();
    salesGroups.forEach(g => {
      if (!criteriaMap.has(g.criteria)) criteriaMap.set(g.criteria, { sales: [], service: [] });
      criteriaMap.get(g.criteria)!.sales.push(g);
    });
    serviceGroups.forEach(g => {
      if (!criteriaMap.has(g.criteria)) criteriaMap.set(g.criteria, { sales: [], service: [] });
      criteriaMap.get(g.criteria)!.service.push(g);
    });

    const categories: any[] = [];

    criteriaMap.forEach((groups, criteriaKey) => {
      const criteria: any[] = [];

      // Get unique subcriteria
      const subCriteriaSet = new Set<string>();
      groups.sales.forEach(g => subCriteriaSet.add(g.subCriteria));
      groups.service.forEach(g => subCriteriaSet.add(g.subCriteria));

      subCriteriaSet.forEach(subKey => {
        const salesGroup = groups.sales.find(g => g.subCriteria === subKey);
        const serviceGroup = groups.service.find(g => g.subCriteria === subKey);

        const salesQuestions: any[] = [];
        const serviceQuestions: any[] = [];

        /** ---------------- SALES QUESTIONS ---------------- */
        if (salesGroup) {
          salesGroup.questions.forEach((q, idx) => {
            const referenceNumber = extractReferenceNumber(q.label);
            const maxScore = getQuestionMaxScore(q);
            const actualScore = calculateQuestionScore(q);

            /** Calculate response distribution (Yes/No/N/A) */
            const responseCounts: Record<string, number> = {};
            let totalResponses = 0;

            q.options.forEach(opt => {
              const cleanOptLabel = cleanLabel(opt.label);
              if (!responseCounts[cleanOptLabel]) responseCounts[cleanOptLabel] = 0;
              if (opt.checked === 1) {
                responseCounts[cleanOptLabel]++;
                totalResponses++;
              }
            });

            const responsePercentages: Record<string, number> = {};
            Object.keys(responseCounts).forEach(label => {
              responsePercentages[label] = totalResponses > 0
                ? Math.round((responseCounts[label] / totalResponses) * 100)
                : 0;
            });

            salesQuestions.push({
              id: referenceNumber || `Q${idx + 1}`,
              question: cleanLabel(q.label),
              maxScore,
              actualScore,
              response: getQuestionResponse(q),
              options: q.options,
              // NEW FIELDS:
              responseCounts,
              responsePercentages,
              totalResponses
            });
          });
        }

        /** ---------------- SERVICE QUESTIONS ---------------- */
        if (serviceGroup) {
          serviceGroup.questions.forEach((q, idx) => {
            const referenceNumber = extractReferenceNumber(q.label);
            const maxScore = getQuestionMaxScore(q);
            const actualScore = calculateQuestionScore(q);

            const responseCounts: Record<string, number> = {};
            let totalResponses = 0;

            q.options.forEach(opt => {
              const cleanOptLabel = cleanLabel(opt.label);
              if (!responseCounts[cleanOptLabel]) responseCounts[cleanOptLabel] = 0;
              if (opt.checked === 1) {
                responseCounts[cleanOptLabel]++;
                totalResponses++;
              }
            });

            const responsePercentages: Record<string, number> = {};
            Object.keys(responseCounts).forEach(label => {
              responsePercentages[label] = totalResponses > 0
                ? Math.round((responseCounts[label] / totalResponses) * 100)
                : 0;
            });

            serviceQuestions.push({
              id: referenceNumber || `Q${idx + 1}`,
              question: cleanLabel(q.label),
              maxScore,
              actualScore,
              response: getQuestionResponse(q),
              options: q.options,
              responseCounts,
              responsePercentages,
              totalResponses
            });
          });
        }

        // Push subCriteriaDetails
        const subCriteriaDetails = [
          {
            id: subKey,
            name: getSubCriteriaDisplayName(subKey),
            salesQuestions,
            serviceQuestions,
            questions: salesQuestions.length > 0 ? salesQuestions : serviceQuestions
          }
        ];

        // Calculate average actualScore for criteria
        const avgSalesActual = salesQuestions.length > 0
          ? salesQuestions.reduce((s, q) => s + q.actualScore, 0) / salesQuestions.length
          : 0;
        const avgServiceActual = serviceQuestions.length > 0
          ? serviceQuestions.reduce((s, q) => s + q.actualScore, 0) / serviceQuestions.length
          : 0;

        criteria.push({
          id: subKey,
          mainCriteria: getSubCriteriaDisplayName(subKey),
          sales: {
            maxScore: salesGroup?.maxScore || 0,
            actualScore: Math.round(avgSalesActual * 100) / 100
          },
          service: {
            maxScore: serviceGroup?.maxScore || 0,
            actualScore: Math.round(avgServiceActual * 100) / 100
          },
          subCriteriaDetails
        });
      });

      categories.push({
        id: criteriaKey,
        name: getCriteriaDisplayName(criteriaKey),
        criteria
      });
    });

    /** Total Sales/Service scores */
    let salesMaxTotal = 0, salesActualTotal = 0, serviceMaxTotal = 0, serviceActualTotal = 0;
    categories.forEach(cat => {
      cat.criteria.forEach((c: any) => {
        salesMaxTotal += c.sales.maxScore || 0;
        salesActualTotal += c.sales.actualScore || 0;
        serviceMaxTotal += c.service.maxScore || 0;
        serviceActualTotal += c.service.actualScore || 0;
      });
    });

    return {
      categories,
      totals: {
        sales: {
          maxScore: salesMaxTotal,
          actualScore: Math.round(salesActualTotal * 100) / 100,
          percentage: salesMaxTotal > 0
            ? Math.round((salesActualTotal / salesMaxTotal) * 100)
            : 0
        },
        service: {
          maxScore: serviceMaxTotal,
          actualScore: Math.round(serviceActualTotal * 100) / 100,
          percentage: serviceMaxTotal > 0
            ? Math.round((serviceActualTotal / serviceMaxTotal) * 100)
            : 0
        }
      }
    };
  } catch (error) {
    console.error('Error transforming preAPI data:', error);
    return { categories: [], totals: { sales: {}, service: {} } };
  }
}


// Helper functions for display names
function getCriteriaDisplayName(criteria: string): string {
  const displayNames: { [key: string]: string } = {
    'environment': 'Environment',
    'social': 'Social',
    'governance': 'Governance',
    'general': 'General'
  };
  return displayNames[criteria] || criteria;
}

function getSubCriteriaDisplayName(subCriteria: string): string {
  const displayNames: { [key: string]: string } = {
    'legal_compliance': 'Legal Compliance',
    'waste_management': 'Waste Management',
    'water_management': 'Water Management',
    'wastewater_management': 'Wastewater Management',
    'energy_management': 'Energy Management',
    'fire_safety': 'Fire Safety & Emergency Preparedness',
    'road_safety': 'Road Safety',
    'electrical_safety': 'Electrical Safety',
    'office_safety': 'Office Safety',
    'sustainability_ambassador_program': 'Sustainability Ambassador Program',
    'legal_requirement': 'Legal Requirement',
    'fair_business_practices': 'Fair Business Practices',
    'data_privacy': 'Data Privacy',
    '1s_2s': '1S & 2S'
  };
  return displayNames[subCriteria] || subCriteria.replace(/_/g, ' ');
}


