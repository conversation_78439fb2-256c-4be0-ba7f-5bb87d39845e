
import React, { useState, useEffect } from 'react';
import { transformPreApiData, getFilterOptions, getFilteredOptions } from '../utils/dataTransform';
import SalesHeatmap from './SalesHeatmap';
import ServiceHeatmap from './ServiceHeatmap';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const MSIHeatmap = () => {
  const [msiData, setMsiData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [globalFilter, setGlobalFilter] = useState('All');
  const [zoneFilter, setZoneFilter] = useState('All');
  const [locationFilter, setLocationFilter] = useState('All');
  const [dealerFilter, setDealerFilter] = useState('All');
  const [areaOfficeFilter, setAreaOfficeFilter] = useState('All');
  const [categoryFilter, setCategoryFilter] = useState('All');

  // Get all available filter options from API
  const [allFilterOptions, setAllFilterOptions] = useState({
    zones: ['All'],
    locations: ['All'],
    dealers: ['All'],
    areaOffices: ['All'],
    categories: ['All']
  });

  // Get filtered options based on current selections
  const [filteredOptions, setFilteredOptions] = useState({
    locations: ['All'],
    dealers: ['All'],
    areaOffices: ['All'],
    categories: ['All']
  });

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const data = await transformPreApiData();

       
        setMsiData(data);
        setError(null);
      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Load filter options
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const options = await getFilterOptions();
        setAllFilterOptions(options);
      } catch (err) {
        console.error('Error loading filter options:', err);
      }
    };

    loadFilterOptions();
  }, []);

  // Update filtered options when filters change
  useEffect(() => {
    const updateFilteredOptions = async () => {
      try {
        const options = await getFilteredOptions({
          zone: zoneFilter,
          location: locationFilter,
          dealer: dealerFilter,
          category: categoryFilter
        });
        setFilteredOptions(options);
      } catch (err) {
        console.error('Error updating filtered options:', err);
      }
    };

    updateFilteredOptions();
  }, [zoneFilter, locationFilter, dealerFilter, categoryFilter]);

  // Force dependent filters to reset when parent filters change
  useEffect(() => {
    setLocationFilter('All');
    setDealerFilter('All');
    setAreaOfficeFilter('All');
    setCategoryFilter('All');
  }, [zoneFilter]);

  useEffect(() => {
    setDealerFilter('All');
    setAreaOfficeFilter('All');
  }, [locationFilter, categoryFilter]);

  useEffect(() => {
    setAreaOfficeFilter('All');
  }, [dealerFilter]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">Loading heatmap data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !msiData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <p className="text-lg text-red-600">{error || 'Failed to load data'}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const { dealerInfo, totals } = msiData;













  // Handle zone change and reset dependent filters
  const handleZoneChange = (newZone: string) => {
    console.log('Zone changed from', zoneFilter, 'to', newZone);
    setZoneFilter(newZone);
    setLocationFilter('All');
    setDealerFilter('All');
    setAreaOfficeFilter('All');
    setCategoryFilter('All');
  };

  // Handle location change and reset dependent filters
  const handleLocationChange = (newLocation: string) => {
    console.log('Location changed from', locationFilter, 'to', newLocation);
    setLocationFilter(newLocation);
    setDealerFilter('All');
    setAreaOfficeFilter('All');
  };

  // Handle category change and reset dependent filters
  const handleCategoryChange = (newCategory: string) => {
    console.log('Category changed from', categoryFilter, 'to', newCategory);
    setCategoryFilter(newCategory);
    setDealerFilter('All');
    setAreaOfficeFilter('All');
  };



  // Handle dealer change and reset dependent filters
  const handleDealerChange = (newDealer: string) => {
    setDealerFilter(newDealer);
    setAreaOfficeFilter('All');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8 overflow-visible">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Dealer MSI Calibration Score
          </h1>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-gradient-to-br from-blue-400 to-blue-500 text-white shadow-xl">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Sales Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{totals.sales.percentage}%</div>
              <div className="text-blue-100 text-sm">
                {totals.sales.actualScore} / {totals.sales.maxScore}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-400 to-purple-500 text-white shadow-xl">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Service Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{totals.service.percentage}%</div>
              <div className="text-purple-100 text-sm">
                {totals.service.actualScore} / {totals.service.maxScore}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-sky-300 to-sky-400 text-white shadow-xl">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Overall Dealership Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{totals.dealershipScore}%</div>
              <div className="text-sky-100 text-sm">Combined Average</div>
            </CardContent>
          </Card>
        </div>

        {/* Zone, Location, Category, Dealer, Area Office Filters */}
        <div className="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/20 overflow-visible mb-4">
          <div className="flex flex-col lg:flex-row gap-4 justify-center items-center flex-wrap">
            {/* Zone Filter */}
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-semibold text-gray-800 text-center">Zone</h3>
              <select
                value={zoneFilter}
                onChange={(e) => handleZoneChange(e.target.value)}
                className="w-48 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
              >
                {allFilterOptions.zones.map((zone) => (
                  <option key={zone} value={zone}>
                    {zone}
                  </option>
                ))}
              </select>
            </div>

            {/* Location Filter */}
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-semibold text-gray-800 text-center">Location</h3>
              <select
                value={locationFilter}
                onChange={(e) => handleLocationChange(e.target.value)}
                className="w-48 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
              >
                {filteredOptions.locations.map((location) => (
                  <option key={location} value={location}>
                    {location}
                  </option>
                ))}
              </select>
            </div>

            {/* Category Filter */}
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-semibold text-gray-800 text-center">Category</h3>
              <select
                value={categoryFilter}
                onChange={(e) => handleCategoryChange(e.target.value)}
                className="w-48 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
              >
                {filteredOptions.categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {/* Dealer Filter */}
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-semibold text-gray-800 text-center">Dealer</h3>
              <select
                value={dealerFilter}
                onChange={(e) => handleDealerChange(e.target.value)}
                className="w-48 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
              >
                {filteredOptions.dealers.map((dealer) => (
                  <option key={dealer} value={dealer}>
                    {dealer}
                  </option>
                ))}
              </select>
            </div>

            {/* Area Office Filter */}
            <div className="flex flex-col space-y-2">
              <h3 className="text-lg font-semibold text-gray-800 text-center">Area Office</h3>
              <select
                value={areaOfficeFilter}
                onChange={(e) => setAreaOfficeFilter(e.target.value)}
                className="w-48 px-4 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
              >
                {filteredOptions.areaOffices.map((office) => (
                  <option key={office} value={office}>
                    {office}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Performance Legend and Global Filter */}
        <div className="bg-white/50 backdrop-blur-sm rounded-lg p-3 border border-white/20 mb-6">
          <div className="flex items-center justify-between">
            {/* Performance Legend */}
            <div className="flex items-center gap-6">
              <span className="text-sm font-medium text-gray-700">Performance Legend:</span>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded"></div>
                <span className="text-xs text-gray-600">100%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-400 rounded"></div>
                <span className="text-xs text-gray-600">80-99%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-400 rounded"></div>
                <span className="text-xs text-gray-600">60-79%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-orange-400 rounded"></div>
                <span className="text-xs text-gray-600">40-59%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-400 rounded"></div>
                <span className="text-xs text-gray-600">20-39%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-600 rounded"></div>
                <span className="text-xs text-gray-600">&lt;20%</span>
              </div>
            </div>

            {/* Global Filter */}
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700">Global Filter:</span>
              <select
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="px-3 py-1 text-sm bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700"
              >
                <option value="All">All</option>
                <option value="Compliance Issues">Regulatory Questions</option>
              </select>
            </div>
          </div>
        </div>



        {/* Heatmaps */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <SalesHeatmap
            categories={msiData.categories || []}
            globalFilter={globalFilter}
            zoneFilter={zoneFilter}
            locationFilter={locationFilter}
            categoryFilter={categoryFilter}
            dealerFilter={dealerFilter}
            areaOfficeFilter={areaOfficeFilter}
          />
          <ServiceHeatmap
            categories={msiData.categories || []}
            globalFilter={globalFilter}
            zoneFilter={zoneFilter}
            locationFilter={locationFilter}
            categoryFilter={categoryFilter}
            dealerFilter={dealerFilter}
            areaOfficeFilter={areaOfficeFilter}
          />
        </div>
      </div>
    </div>
  );
};

export default MSIHeatmap;
