import React from 'react';
import { getVendorDetails } from '../utils/dataTransform';

interface FilterCriteria {
  zone?: string;
  location?: string;
  dealer?: string;
  areaOffice?: string;
  category?: string;
}

interface DataFilterProps {
  rawData: any[];
  filters: FilterCriteria;
}

/**
 * DataFilter hook that filters raw API data based on provided criteria
 * @param rawData - Raw API data array
 * @param filters - Filter criteria object
 * @returns Filtered data array
 */
export const useDataFilter = ({ rawData, filters }: DataFilterProps): any[] => {
  if (!rawData || rawData.length === 0) {
    return [];
  }

  console.log('Filtering data with criteria:', filters);
  console.log('Raw data length:', rawData.length);

  const filtered = rawData.filter(dataObject => {
    // Get vendor details using the helper function
    const vendorDetails = getVendorDetails(rawData, dataObject.vendorCode, dataObject.dealerId);

    console.log('Vendor details for filtering:', vendorDetails);

    // Apply zone filter
    if (filters.zone && filters.zone !== 'All') {
      if (vendorDetails.zone !== filters.zone) {
        console.log(`Zone filter: ${vendorDetails.zone} !== ${filters.zone}, excluding`);
        return false;
      }
    }

    // Apply location filter
    if (filters.location && filters.location !== 'All') {
      if (vendorDetails.city !== filters.location) {
        console.log(`Location filter: ${vendorDetails.city} !== ${filters.location}, excluding`);
        return false;
      }
    }

    // Apply dealer filter
    if (filters.dealer && filters.dealer !== 'All') {
      if (vendorDetails.dealerName !== filters.dealer) {
        console.log(`Dealer filter: ${vendorDetails.dealerName} !== ${filters.dealer}, excluding`);
        return false;
      }
    }

    // Apply area office filter
    if (filters.areaOffice && filters.areaOffice !== 'All') {
      if (vendorDetails.areaOffice !== filters.areaOffice) {
        console.log(`Area office filter: ${vendorDetails.areaOffice} !== ${filters.areaOffice}, excluding`);
        return false;
      }
    }

    // Apply category filter
    if (filters.category && filters.category !== 'All') {
      if (vendorDetails.category !== filters.category) {
        console.log(`Category filter: ${vendorDetails.category} !== ${filters.category}, excluding`);
        return false;
      }
    }

    return true;
  });

  console.log('Filtered data length:', filtered.length);
  return filtered;
};

export type { FilterCriteria, DataFilterProps };
export default useDataFilter;
